import uuid
import streamlit as st
from procleg.backend.chatbot.multiagent_graph import multi_agent_graph
from conversation_thread import load_conversations,save_conversation

#prid & module need to be taken during authentication
prid = "kjlc847"
module = "procurement"

if 'thread_id' not in st.session_state:
    st.session_state['thread_id'] = str(uuid.uuid4())

if 'chat_history' not in st.session_state:
    st.session_state['chat_history'] = []


conversations = load_conversations(prid)

for conversation in conversations:
    thread_name = conversation.get("thread_name", "Untitled")
    # pprint.pprint(conversation)
    if st.button(thread_name, key=conversation.get("thread_id", "unknown")):
        st.session_state['chat_history'] = []
        st.session_state['thread_id'] = conversation.get("thread_id", "unknown")
        st.session_state['messages'] = []
        for message in conversation["text"]:
            role = message["role"]
            content = message["content"]
            st.session_state['chat_history'].append({'role': role, 'content': content})
            print("appended the message to chat_history:",{'role': role, 'content': content} )


#Displaying Chat History on UI
for message in st.session_state['chat_history']:
    role = message["role"]
    content = message["content"]
    st.chat_message(role).write(content)

user_input = st.chat_input("Enter your queries.")

config = {"configurable": {"thread_id": st.session_state['thread_id']}}


if user_input:
    st.chat_message("user").write(user_input)
    st.session_state['chat_history'].append({'role': "user", 'content': user_input})

    for event in multi_agent_graph.stream({"messages": st.session_state['chat_history']},
                                           config, stream_mode="values"):
        ai_resp = event["messages"][-1].content
        # print("---event---", event)

    st.session_state['chat_history'].append({'role': "assistant", 'content': ai_resp })
    st.chat_message("assistant").write(ai_resp)

    save_conversation(st.session_state['chat_history'], prid, st.session_state['thread_id'], module, None)
