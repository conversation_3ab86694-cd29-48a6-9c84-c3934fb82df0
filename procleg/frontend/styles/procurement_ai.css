/* Modern CSS Variables for consistent theming */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #10b981;
  --secondary-hover: #059669;
  --accent-color: #f59e0b;
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --border-hover: #cbd5e1;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

.st-key-ai_assistant_container {
  height: 100% !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.chat-container {
  margin-top: 0px;
  background: var(--background-primary);
}

.chat-message {
  padding: 12px 16px;
  border-radius: var(--radius-lg);
  margin-bottom: 12px;
  transition: all 0.2s ease-in-out;
  box-shadow: var(--shadow-sm);
}

.user-message {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
  text-align: left;
}

.ai-message {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
}

.st-key-user_input .stTextArea textarea {
  caret-color: var(--primary-color) !important;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  border-radius: var(--radius-lg) !important;
  border: 2px solid var(--border-color) !important;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.st-key-user_input .stTextArea textarea:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1) !important;
  outline: none !important;
}

.st-bq {
  background-color: var(--background-tertiary) !important;
}

.st-bv {
  color: var(--text-primary);
}

.st-key-user_input .stTextArea textarea::placeholder {
  color: var(--text-muted) !important;
  font-style: italic;
}

.st-key-text_area_container {
  margin-top: 0.5rem !important;
  margin-left: 1rem !important;
  margin-right: 1rem !important;
  width: calc(100% - 2rem) !important;
  margin-bottom: 0.5rem !important;
  background: var(--background-primary);
  padding: 12px;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  position: relative !important;
}

.input-flex-container {
  display: flex !important;
  gap: 12px !important;
  align-items: center !important;
}

.st-key-send_btn_wrapper {
  flex-shrink: 0 !important;
}

.st-key-send_btn {
  margin-top: 0 !important;
}

.st-key-send_btn button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: var(--radius-md) !important;
  padding: 8px 16px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  transition: all 0.2s ease-in-out !important;
  box-shadow: var(--shadow-sm) !important;
  cursor: pointer !important;
  width: 80px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 80px !important;
}

.st-key-send_btn button:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, #1e40af 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-md) !important;
}

.st-key-send_btn button:active {
  transform: translateY(0) !important;
  box-shadow: var(--shadow-sm) !important;
}

.st-key-history_container .stButton button {
  width: 100%;
  padding: 12px 16px;
  border: none;
  background-color: transparent !important;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer !important;
  all: unset !important;
  display: inline-block !important;
  text-transform: none !important;
  border-radius: var(--radius-md) !important;
  transition: all 0.2s ease-in-out !important;
  text-align: left !important;
  line-height: 1.4 !important;
}

.st-key-history_container .stButton button:hover {
  background-color: var(--background-secondary) !important;
  color: var(--primary-color) !important;
  transform: translateX(4px) !important;
}

.st-key-history_container {
  background: linear-gradient(180deg, var(--background-primary) 0%, var(--background-secondary) 100%) !important;
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  padding: 24px 16px;
  margin-top: 0rem;
  margin-left: -5rem;
  width: calc(100vw * 1 / 6);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  color: var(--text-primary) !important;
  min-height: calc(100vh - 127px);
  max-height: calc(100vh - 127px);
  box-shadow: var(--shadow-lg);
  border-right: 1px solid var(--border-color);
  overflow-y: auto !important;
  overflow-x: hidden;
  scroll-behavior: smooth;
  backdrop-filter: blur(10px);
  position: relative !important;
}

.st-key-history_container::-webkit-scrollbar {
  width: 8px;
}

.st-key-history_container::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--border-hover) 0%, var(--text-muted) 100%);
  border-radius: var(--radius-lg);
  border: 2px solid transparent;
  background-clip: content-box;
}

.st-key-history_container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: var(--radius-lg);
}

.st-key-history_container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--text-muted) 0%, var(--text-secondary) 100%) !important;
}

.st-key-history_container > div > div {
  gap: 5px !important;
}

.st-key-history_container .stExpander {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
  padding-bottom: 0 !important;
}

.st-key-history_container .stExpander details {
  border: none !important;
  box-shadow: none !important;
  border-radius: var(--radius-md) !important;
  background: var(--background-secondary);
  margin-bottom: 4px !important;
  overflow: hidden !important;
}

.st-key-history_container .stExpander details[open] {
  background: var(--background-primary) !important;
  border: 1px solid var(--border-color) !important;
}

.st-key-history_container .stHorizontalBlock {
  flex-grow: 0 !important;
}

.st-key-history_container .stExpander details summary {
  padding: 8px 12px !important;
  background-color: transparent !important;
  border-radius: var(--radius-md);
  font-weight: 600 !important;
  font-size: 13px !important;
  color: var(--text-primary) !important;
  transition: all 0.2s ease-in-out !important;
  cursor: pointer !important;
  min-height: 32px !important;
  display: flex !important;
  align-items: center !important;
}

.st-key-history_container .stExpander details summary:hover {
  color: var(--primary-color) !important;
  background-color: var(--background-primary) !important;
}

/* Fix expander functionality and prevent scroll issues */
.st-key-history_container .stExpander details {
  scroll-margin: 0 !important;
  scroll-behavior: auto !important;
}

.st-key-history_container .stExpander details summary {
  scroll-margin: 0 !important;
  scroll-behavior: auto !important;
  position: relative !important;
}

/* Prevent automatic scrolling on expander click */
.st-key-history_container .stExpander details summary:focus {
  scroll-margin: 0 !important;
  outline: none !important;
}

/* Fix expander arrow functionality */
.st-key-history_container .stExpander details summary::-webkit-details-marker {
  display: none !important;
}

.st-key-history_container .stExpander details summary::before {
  content: "▶" !important;
  font-size: 12px !important;
  margin-right: 8px !important;
  transition: transform 0.2s ease-in-out !important;
  display: inline-block !important;
}

.st-key-history_container .stExpander details[open] summary::before {
  content: "▼" !important;
  transform: none !important;
}

.st-key-history_container
  .stExpander
  details
  [data-testid="stExpanderDetails"] {
  padding-left: 3px !important;
  margin-top: 0.5rem !important;
}

.st-key-history_container
  .stExpander
  details
  [data-testid="stExpanderDetails"]
  [data-testid="stVerticalBlock"] {
  gap: 0.3rem !important;


}

/* Fix expander content styling */
.st-key-history_container .stExpander [data-testid="stExpanderDetails"] {
  padding: 0.25rem !important;
  margin: 0 !important;
}

.st-key-history_container .stExpander [data-testid="stExpanderDetails"] > div {
  gap: 0.2rem !important;
}

/* Conversation button styling inside expanders */
.st-key-history_container .stExpander .stButton button {
  width: 100% !important;
  padding: 6px 8px !important;
  font-size: 12px !important;
  box-sizing: border-box !important;
  border-radius: var(--radius-sm) !important;
  text-align: left !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  min-height: 28px !important;
  line-height: 1.2 !important;
  background: transparent !important;
  border: 1px solid transparent !important;
  color: var(--text-secondary) !important;
  margin-bottom: 2px !important;
}

.st-key-history_container .stExpander .stButton button:hover {
  background-color: var(--background-primary) !important;
  border-color: var(--border-color) !important;
  color: var(--primary-color) !important;
  cursor: pointer !important;
  transform: translateX(2px) !important;
}

.st-key-text_area_container .stTextArea textarea {
  border-radius: var(--radius-lg) !important;
  border: 2px solid var(--border-color) !important;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out !important;
  background: var(--background-primary) !important;
  resize: none !important;
}

.st-key-text_area_container .stTextArea textarea:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1) !important;
  outline: none !important;
}

/* Chat header styling */
.chat-header-container {
  position: relative !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 1rem !important;
  margin-top: -0.8rem !important;
}

.chat-header-title {
  margin: 0 !important;
  font-weight: bold !important;
  color: var(--text-primary) !important;
  font-size: 18px !important;
}

.st-key-new_chat_btn {
  position: absolute !important;
  top: -0.8rem !important;
  right: 0 !important;
  z-index: 10 !important;
}

.st-key-new_chat_btn button[data-testid="stBaseButton-secondary"] {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-hover) 100%) !important;
  margin: 0 !important;
  color: white !important;
  border-radius: var(--radius-md) !important;
  padding: 8px 12px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  min-width: 40px !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  white-space: nowrap !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  box-shadow: var(--shadow-md) !important;
  transition: all 0.2s ease-in-out !important;
  cursor: pointer !important;
}

.st-key-new_chat_btn button[data-testid="stBaseButton-secondary"]:hover {
  background: linear-gradient(135deg, var(--secondary-hover) 0%, #047857 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.st-key-new_chat_btn button[data-testid="stBaseButton-secondary"]:active {
  transform: translateY(0) !important;
  box-shadow: var(--shadow-md) !important;
}

#chat-scroll {
  flex: 1 1 auto;
  height: auto !important;
  min-height: 0;
  max-height: none;
  display: flex;
  flex-direction: column;
}

.st-key-history_container .stExpander details summary,
.st-key-history_container .stExpander details summary * {
  font-weight: bold !important;
}

.st-key-chat_container{
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  padding-bottom: 1rem !important;
  padding-top: 0.5rem !important;
}

/* Canvas header styling */
.canvas-header-container {
  position: relative !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 8px !important;
  padding: 8px 0 !important;
}

.canvas-header-title {
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1e293b !important;
  font-family: inherit !important;
}

/* Additional targeting to ensure visibility */
.canvas-header-container h3 {
  color: #1e293b !important;
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
}

.st-key-editor_header h3 {
  color: #1e293b !important;
}

.st-key-close_canvas {
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  z-index: 10 !important;
}

.st-key-close_canvas button {
  background: var(--background-secondary) !important;
  color: var(--text-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-md) !important;
  padding: 4px 8px !important;
  font-size: 16px !important;
  font-weight: bold !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease-in-out !important;
}

.st-key-close_canvas button:hover {
  background: var(--background-tertiary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-hover) !important;
}

.st-key-editor_header {
  margin-top: 0.5rem !important;
  background: var(--background-primary);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  padding: 12px 16px 8px 16px;
  border: 1px solid var(--border-color);
  border-bottom: none;
  position: relative !important;
}

.st-key-editor_container {
  margin-top: 0 !important;
  background: var(--background-primary);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  border: 1px solid var(--border-color);
  border-top: none;
  box-shadow: var(--shadow-md);
}

.download-btn {
  background: linear-gradient(135deg, var(--accent-color) 0%, #d97706 100%);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 8px 16px;
  text-decoration: none;
  display: inline-block;
  margin-right: 12px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease-in-out;
  box-shadow: var(--shadow-sm);
}

.download-btn:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.stDownloadButton button{
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-hover) 100%) !important;
  color: white !important;
  white-space: nowrap !important;
  border: none !important;
  border-radius: var(--radius-lg) !important;
  padding: 12px 20px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  transition: all 0.2s ease-in-out !important;
  box-shadow: var(--shadow-md) !important;
  cursor: pointer !important;
}

.stDownloadButton button:hover{
  border: none !important;
  background: linear-gradient(135deg, var(--secondary-hover) 0%, #047857 100%) !important;
  color: white !important;
  white-space: nowrap !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.open-btn-wrapper button {
  background-color: #238b8b !important;
  border-radius: 5px;
  padding: 6px 12px;
  border: 1px solid #ccc;
  font-size: 0.9rem;
  cursor: pointer;
}

.chat-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  animation: fadeInUp 0.3s ease-out;
  opacity: 0;
  animation-fill-mode: forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-row {
  flex-direction: row;
}
.ai-row {
  flex-direction: row;
}
.avatar {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 12px;
  margin-right: 12px;
  margin-left: 8px;
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease-in-out;
}

.avatar:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.user-avatar {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: var(--text-primary);
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-color);
}
.ai-avatar {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
  border-radius: var(--radius-lg);
  border: 2px solid var(--primary-color);
}
.chat-bubble {
  padding: 12px 16px;
  border-radius: var(--radius-lg);
  font-size: 14px;
  line-height: 1.5;
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  width: 100%;
  min-width: 0;
  word-break: break-word;
  box-sizing: border-box;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease-in-out;
  font-family: inherit;
}

.chat-bubble:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.user-bubble {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
  color: var(--text-primary);
}
.ai-bubble {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  color: var(--text-primary);
  position: relative;
}
.copy-btn {
  color: var(--text-muted);
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--radius-md);
  opacity: 0;
  transition: all 0.2s ease-in-out;
  box-shadow: var(--shadow-sm);
}

.ai-bubble:hover .copy-btn {
  opacity: 1;
}

.copy-btn:hover {
  opacity: 1 !important;
  background-color: var(--background-secondary);
  color: var(--primary-color);
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.copy-btn svg {
  display: block;
  width: 16px;
  height: 16px;
}

.copy-tooltip {
  position: absolute;
  top: -35px;
  right: 0;
  background: var(--text-primary);
  color: white;
  padding: 6px 12px;
  border-radius: var(--radius-md);
  font-size: 12px;
  font-weight: 500;
  opacity: 0;
  transition: all 0.2s ease-in-out;
  pointer-events: none;
  box-shadow: var(--shadow-md);
  white-space: nowrap;
}
.copy-tooltip.show {
  opacity: 1;
  transform: translateY(-2px);
}

.st-key-chat-scroll {
  min-height: 400px !important;
  max-height: 400px !important;
  overflow-y: auto !important;
  overflow-x: hidden;
  scroll-behavior: smooth;
  padding: 8px 12px;
  background: var(--background-primary);
}

.st-key-chat-scroll::-webkit-scrollbar {
  width: 8px;
}
.st-key-chat-scroll::-webkit-scrollbar-track {
  background: transparent;
  border-radius: var(--radius-lg);
}
.st-key-chat-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--border-hover) 0%, var(--text-muted) 100%);
  border-radius: var(--radius-lg);
  border: 2px solid transparent;
  background-clip: content-box;
}

.st-key-chat-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--text-muted) 0%, var(--text-secondary) 100%);
}

.st-key-chat-scroll .stButton > button {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-hover) 100%) !important;
  color: white !important;
  padding: 12px 24px !important;
  border-radius: var(--radius-xl) !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  position: relative !important;
  top: 0 !important;
  margin: 8px 0 !important;
  margin-left: 4rem !important;
  border: none !important;
  box-shadow: var(--shadow-md) !important;
  transition: all 0.2s ease-in-out !important;
  cursor: pointer !important;
}

.st-key-chat-scroll .stButton > button:hover {
  background: linear-gradient(135deg, var(--secondary-hover) 0%, #047857 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-lg) !important;
}


.st-key-editor_container .element-container:has(iframe) {
  height: 496px !important;
  overflow-y: scroll !important;
  overflow-x: hidden !important;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* Loading states and animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .st-key-history_container {
    width: 100% !important;
    margin-left: 0 !important;
    position: fixed;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .st-key-history_container.mobile-open {
    transform: translateX(0);
  }

  .chat-row {
    margin-bottom: 20px;
  }

  .avatar {
    width: 40px;
    height: 40px;
    margin-right: 12px;
    margin-left: 8px;
  }

  .chat-bubble {
    padding: 16px 20px;
    font-size: 14px;
  }

  .st-key-text_area_container {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
    width: calc(100% - 2rem) !important;
    padding: 12px !important;
  }

  .input-flex-container {
    flex-direction: column !important;
    gap: 8px !important;
    align-items: stretch !important;
  }

  .st-key-send_btn button {
    height: 48px !important;
    font-size: 14px !important;
    width: 100% !important;
  }
}

/* Focus and accessibility improvements */
.st-key-user_input .stTextArea textarea:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.stButton button:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Enhanced scrollbar for better UX */
.st-key-chat-scroll {
  scrollbar-width: thin;
  scrollbar-color: var(--text-muted) transparent;
}

/* Improved typography for better readability */
.chat-bubble h1, .chat-bubble h2, .chat-bubble h3, .chat-bubble h4, .chat-bubble h5, .chat-bubble h6 {
  color: var(--text-primary);
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.chat-bubble p {
  margin-bottom: 1em;
  line-height: 1.6;
}

.chat-bubble ul, .chat-bubble ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.chat-bubble li {
  margin-bottom: 0.5em;
}

.chat-bubble code {
  background: var(--background-tertiary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 0.9em;
  color: var(--text-primary);
}

.chat-bubble pre {
  background: var(--background-tertiary);
  padding: 16px;
  border-radius: var(--radius-md);
  overflow-x: auto;
  margin: 1em 0;
  border: 1px solid var(--border-color);
}

.chat-bubble blockquote {
  border-left: 4px solid var(--primary-color);
  padding-left: 16px;
  margin: 1em 0;
  color: var(--text-secondary);
  font-style: italic;
}
