.st-key-ai_assistant_container {
  height: 100% !important;
}

.chat-container {
  margin-top: 0px;
}

.chat-message {
  padding: 10px;
  border-radius: 10px;
  margin-bottom: 10px;
}

.user-message {
  background-color: #e6f7ff;
  text-align: left;
}

.ai-message {
  background-color: #f0f0f0;
}

.st-key-user_input .stTextArea textarea {
  caret-color: rgb(38, 39, 48) !important;
}

.st-bq {
  background-color: #e6e7e6 !important;
}

.st-bv {
  color: rgb(38, 39, 48);
}

.st-key-user_input .stTextArea textarea::placeholder {
  color: #333 !important;
}

.st-key-text_area_container {
  margin-top: -0.5rem !important;
  margin-left: 1.7rem !important;
  width: 100%;
  margin-bottom: 0 !important;
  position: relative !important;
}

.st-key-send_btn {
  position: absolute !important;
  right: -6rem !important;
  bottom: 1.5rem !important;
  margin: 0 !important;
}

.st-key-history_container .stButton button {
  width: 100%;
  padding: 8px;
  border: none;
  background-color: transparent !important;
  color: #333;
  font-size: 1em;
  font-weight: bold;
  cursor: pointer !important;
  all: unset !important;
  display: inline-block !important;
  text-transform: capitalize !important;
}

.st-key-history_container {
  background: #e6e7e6 !important;
  border-radius: 0;
  padding: 1rem;
  margin-top: 0rem;
  margin-left: -5rem;
  width: calc(100vw * 1 / 6);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  color: rgb(42, 41, 41) !important;
  min-height: calc(100vh - 127px);
  max-height: calc(100vh - 127px);
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  border-right: 0.5px solid #ccc;
  overflow-y: auto !important;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

.st-key-history_container::-webkit-scrollbar {
  width: 6px;
}

.st-key-history_container::-webkit-scrollbar-thumb {
  background: #b0b0b0;
  border-radius: 8px;
}

.st-key-history_container::-webkit-scrollbar-track {
  background: #e6e7e6;
}

.st-key-history_container::-webkit-scrollbar-thumb:hover {
  background: #888 !important;
}

.st-key-history_container > div > div {
  gap: 5px !important;
}

.st-key-history_container .stExpander {
  margin-top: -3rem !important;
  padding-bottom: 2rem !important;
}

.st-key-history_container .stExpander details {
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.st-key-history_container .stHorizontalBlock {
  flex-grow: 0 !important;
}

.st-key-history_container .stExpander details summary {
  padding: 0.5rem !important;
  background-color: transparent !important;
  border-radius: 10px;
  font-weight: bold !important;
}

.st-key-history_container .stExpander details summary:hover {
  color: #333 !important;
}

.st-key-history_container
  .stExpander
  details
  [data-testid="stExpanderDetails"] {
  padding-left: 3px !important;
  margin-top: 0.5rem !important;
}

.st-key-history_container
  .stExpander
  details
  [data-testid="stExpanderDetails"]
  [data-testid="stVerticalBlock"] {
  gap: 0.3rem !important;


}

.st-key-history_container
  .stExpander
  details
  [data-testid="stExpanderDetails"]
  [data-testid="stVerticalBlock"] .stButton button {
  width: 100% !important;
  padding: 1px 2px !important;
  font-size: 1.1em !important;
  box-sizing: border-box !important;
  border-radius: 5px !important;
  text-align: left !important;
  white-space: normal !important;
}

.st-key-history_container
  .stExpander
  details
  [data-testid="stExpanderDetails"]
  [data-testid="stVerticalBlock"] .stButton button:hover {
  background-color: #cdcecd !important;
  border-radius: 5px !important;
  cursor: pointer !important;
}

.st-key-text_area_container > div > div {
  margin-right: 8rem !important;
}

.st-key-new_chat_btn button[data-testid="stBaseButton-secondary"] {
  background-color: #2ea6a6 !important;
  margin-top: -4.1rem !important;
  color: #fff !important;
  border-radius: 15px !important;
  padding: 0.4rem !important;
  font-size: 1.1em !important;
  font-weight: 500 !important;
  margin-bottom: -1.5rem !important;
  min-width: 51px !important;
  width: fit-content !important;
  display: block !important;
  margin-left: 9.5rem !important;
  margin-right: auto !important;
  white-space: nowrap !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.st-key-new_chat_btn button[data-testid="stBaseButton-secondary"]:hover {
  background-color: #238b8b !important;
}

#chat-scroll {
  flex: 1 1 auto;
  height: auto !important;
  min-height: 0;
  max-height: none;
  display: flex;
  flex-direction: column;
}

.st-key-history_container .stExpander details summary,
.st-key-history_container .stExpander details summary * {
  font-weight: bold !important;
}

.st-key-chat_container{
  padding-left: 3rem !important;
}

.canvas-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color:#333
}
.st-key-editor_header {
  margin-top: 0.5rem !important;
}

.st-key-editor_container {
  margin-top: -1.9rem !important;
}

.download-btn {
  background-color: #8ab4f8;
  color: #202124;
  border: none;
  border-radius: 4px;
  padding: 5px 15px;
  text-decoration: none;
  display: inline-block;
  margin-right: 10px;
}

.stDownloadButton button{
  background-color:  #238b8b !important;
  color: white !important;
  white-space: nowrap !important;
}

.stDownloadButton button:hover{
  border:none !important;
  background-color:  #238b8b !important;
  color: white !important;
  white-space: nowrap !important;
}

.open-btn-wrapper button {
  background-color: #238b8b !important;
  border-radius: 5px;
  padding: 6px 12px;
  border: 1px solid #ccc;
  font-size: 0.9rem;
  cursor: pointer;
}

.chat-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
}
.user-row {
  flex-direction: row;
}
.ai-row {
  flex-direction: row;
}
.avatar {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1em;
  margin-right: 18px;
  margin-left: 8px;
}
.user-avatar {
  background: #e0e0e0;
  color: #222;
  border-radius: 6px;
}
.ai-avatar {
  background: #20b8c7;
  color: #fff;
  border-radius: 6px;
}
.chat-bubble {
  padding: 18px 32px;
  border-radius: 32px;
  font-size: 1.08em;
  background: #fff;
  border: 2px solid #eee;
  width: 100%;
  min-width: 0;
  word-break: break-word;
  box-sizing: border-box;
}
.user-bubble {
  border: 2px solid #ff9800;
  color: #222;
}
.ai-bubble {
  border: 2px solid #20b8c7;
  color: #222;
  position: relative;
}
.copy-btn {
  color:#333;
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  opacity: 0.6;
  transition: opacity 0.2s, background-color 0.2s;
}

.copy-btn:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

.copy-btn svg {
  display: block;
}

.copy-tooltip {
  position: absolute;
  top: -25px;
  right: 0;
  background: #333;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
  pointer-events: none;
}
.copy-tooltip.show {
  opacity: 1;
}

.st-key-chat-scroll {
  min-height: 520px !important;
  max-height: 520px !important;
  overflow-y: auto !important;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

.st-key-chat-scroll::-webkit-scrollbar {
  width: 6px;
}
.st-key-chat-scroll::-webkit-scrollbar-track {
  background: transparent;
}
.st-key-chat-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.st-key-chat-scroll .stButton > button {
  background-color: #319696 !important;
  color: white !important;
  padding: 10px 20px !important;
  border-radius: 16px !important;
  font-weight: bold !important;
  position: relative !important;
  top: 0 !important;
  margin: 0 !important;
  margin-left: 4rem !important;
  margin-top: -1rem !important;
}
.st-key-close_canvas .stButton > button {
  border-radius: 0.6rem !important;
  padding: 0rem 0.9rem !important;
  top: -0.2rem !important;
}

.st-key-editor_container .element-container:has(iframe) {
  height: 496px !important;
  overflow-y: scroll !important;
  overflow-x: hidden !important;
}
